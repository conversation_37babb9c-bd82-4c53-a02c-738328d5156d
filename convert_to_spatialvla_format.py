#!/usr/bin/env python3
"""
RLBench到SpatialVLA格式转换脚本

该脚本将RLBench生成的示教数据转换为SpatialVLA训练所需的RLDS格式。
主要功能：
1. 加载RLBench原始示教数据
2. 计算增量动作和统计信息
3. 转换为RLDS数据集格式
4. 验证转换后数据的正确性

作者：SpatialVLA Integration Team
日期：2024
"""

import os
import sys
import json
import pickle
import argparse
import numpy as np
import tensorflow as tf
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from scipy.spatial.transform import Rotation
from PIL import Image
import cv2

# 配置常量
SPATIALVLA_IMAGE_SIZE = (224, 224)
ACTION_DIM = 7
NORMALIZATION_SIGMA = 3.0  # 3-sigma归一化

@dataclass
class ConversionConfig:
    """数据转换配置"""
    input_dir: str
    output_dir: str
    task_names: List[str]
    train_split: float = 0.8
    compute_stats: bool = True
    save_images: bool = True
    validate_output: bool = True

class ActionDeltaCalculator:
    """动作增量计算器"""

    @staticmethod
    def compute_position_delta(current_pose: np.ndarray, next_pose: np.ndarray) -> np.ndarray:
        """计算位置增量"""
        return next_pose[:3] - current_pose[:3]

    @staticmethod
    def compute_rotation_delta(current_quat: np.ndarray, next_quat: np.ndarray) -> np.ndarray:
        """计算旋转增量（四元数差分转欧拉角）"""
        # 确保四元数归一化
        current_quat = current_quat / np.linalg.norm(current_quat)
        next_quat = next_quat / np.linalg.norm(next_quat)

        # 计算相对旋转
        current_rot = Rotation.from_quat(current_quat)
        next_rot = Rotation.from_quat(next_quat)

        # 计算增量旋转
        delta_rot = next_rot * current_rot.inv()

        # 转换为欧拉角（roll, pitch, yaw）
        return delta_rot.as_euler('xyz')

    @staticmethod
    def compute_gripper_action(current_open: float, next_open: float) -> float:
        """计算夹爪动作"""
        return float(next_open > 0.5)

    def compute_delta_action(self, current_obs, next_obs) -> np.ndarray:
        """计算7维增量动作向量"""
        # 位置增量
        delta_pos = self.compute_position_delta(
            current_obs.gripper_pose, next_obs.gripper_pose
        )

        # 旋转增量
        delta_rot = self.compute_rotation_delta(
            current_obs.gripper_pose[3:7], next_obs.gripper_pose[3:7]
        )

        # 夹爪动作
        gripper_action = self.compute_gripper_action(
            current_obs.gripper_open, next_obs.gripper_open
        )

        return np.concatenate([delta_pos, delta_rot, [gripper_action]])

class ActionStatistics:
    """动作统计信息计算器"""

    def __init__(self):
        self.actions = []

    def add_actions(self, actions: np.ndarray):
        """添加动作数据"""
        if actions.ndim == 1:
            actions = actions.reshape(1, -1)
        self.actions.append(actions)

    def compute_statistics(self) -> Dict[str, np.ndarray]:
        """计算统计信息"""
        if not self.actions:
            raise ValueError("No actions added")

        all_actions = np.concatenate(self.actions, axis=0)

        stats = {
            'mean': np.mean(all_actions, axis=0),
            'std': np.std(all_actions, axis=0),
            'min': np.min(all_actions, axis=0),
            'max': np.max(all_actions, axis=0),
            'q01': np.percentile(all_actions, 1, axis=0),
            'q99': np.percentile(all_actions, 99, axis=0),
            'count': len(all_actions)
        }

        return stats

    def normalize_action(self, action: np.ndarray, stats: Dict[str, np.ndarray]) -> np.ndarray:
        """使用统计信息归一化动作"""
        normalized = (action - stats['mean']) / (NORMALIZATION_SIGMA * stats['std'])
        return np.clip(normalized, -1.0, 1.0)

class ObservationProcessor:
    """观测数据处理器"""

    def __init__(self, target_size: Tuple[int, int] = SPATIALVLA_IMAGE_SIZE):
        self.target_size = target_size

    def process_image(self, image: np.ndarray) -> np.ndarray:
        """处理图像数据"""
        if image is None:
            return None

        # 确保图像尺寸正确
        if image.shape[:2] != self.target_size:
            image = cv2.resize(image, self.target_size)

        # 确保数据类型和范围
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)

        return image

    def extract_state_vector(self, obs) -> np.ndarray:
        """提取状态向量"""
        state_components = []

        # 夹爪位姿 (7维)
        if hasattr(obs, 'gripper_pose'):
            state_components.append(obs.gripper_pose)

        # 夹爪开合状态 (1维)
        if hasattr(obs, 'gripper_open'):
            state_components.append([float(obs.gripper_open)])

        # 关节位置 (可选)
        if hasattr(obs, 'joint_positions'):
            state_components.append(obs.joint_positions)

        # 关节速度 (可选)
        if hasattr(obs, 'joint_velocities'):
            state_components.append(obs.joint_velocities)

        return np.concatenate(state_components) if state_components else np.array([])

    def process_observation(self, obs) -> Dict[str, Any]:
        """处理单个观测"""
        processed = {}

        # 处理图像
        if hasattr(obs, 'wrist_rgb') and obs.wrist_rgb is not None:
            processed['wrist_rgb'] = self.process_image(obs.wrist_rgb)

        if hasattr(obs, 'left_shoulder_rgb') and obs.left_shoulder_rgb is not None:
            processed['left_shoulder_rgb'] = self.process_image(obs.left_shoulder_rgb)

        # 处理深度图（如果存在）
        if hasattr(obs, 'wrist_depth') and obs.wrist_depth is not None:
            processed['wrist_depth'] = obs.wrist_depth

        if hasattr(obs, 'left_shoulder_depth') and obs.left_shoulder_depth is not None:
            processed['left_shoulder_depth'] = obs.left_shoulder_depth

        # 提取状态向量
        processed['state'] = self.extract_state_vector(obs)

        return processed

class RLDSDatasetBuilder:
    """RLDS数据集构建器"""

    def __init__(self, config: ConversionConfig):
        self.config = config
        self.action_calculator = ActionDeltaCalculator()
        self.obs_processor = ObservationProcessor()
        self.action_stats = ActionStatistics()

    def load_demos(self, task_name: str) -> List[List]:
        """加载指定任务的示教数据"""
        demo_file = Path(self.config.input_dir) / f"raw_demos_{task_name}.pkl"

        if not demo_file.exists():
            raise FileNotFoundError(f"Demo file not found: {demo_file}")

        with open(demo_file, 'rb') as f:
            demos = pickle.load(f)

        print(f"加载任务 {task_name}: {len(demos)} 个示教")
        return demos

    def process_demo(self, demo: List, task_name: str) -> Dict[str, Any]:
        """处理单个demonstration"""
        processed_steps = []
        demo_actions = []

        for i in range(len(demo) - 1):  # 最后一步没有动作
            current_obs = demo[i]
            next_obs = demo[i + 1]

            # 处理观测
            processed_obs = self.obs_processor.process_observation(current_obs)

            # 计算增量动作
            action = self.action_calculator.compute_delta_action(current_obs, next_obs)
            demo_actions.append(action)

            # 构建步骤数据
            step_data = {
                'observation': processed_obs,
                'action': action,
                'reward': 0.0,  # RLBench示教数据没有奖励
                'is_terminal': False,
                'is_first': (i == 0),
                'task_description': task_name.replace('_', ' ')
            }

            processed_steps.append(step_data)

        # 最后一步标记为终止
        if processed_steps:
            processed_steps[-1]['is_terminal'] = True
            processed_steps[-1]['reward'] = 1.0  # 成功完成任务

        # 收集动作统计信息
        if demo_actions:
            self.action_stats.add_actions(np.array(demo_actions))

        return {
            'steps': processed_steps,
            'episode_length': len(processed_steps),
            'task_name': task_name
        }

    def split_demos(self, demos: List[List], train_split: float) -> Tuple[List, List]:
        """分割训练和验证数据"""
        np.random.shuffle(demos)
        split_idx = int(len(demos) * train_split)
        return demos[:split_idx], demos[split_idx:]

    def save_rlds_dataset(self, episodes: List[Dict], split_name: str, task_name: str):
        """保存RLDS格式数据集"""
        output_dir = Path(self.config.output_dir) / task_name / split_name
        output_dir.mkdir(parents=True, exist_ok=True)

        # 保存每个episode
        for episode_idx, episode in enumerate(episodes):
            episode_dir = output_dir / f"episode_{episode_idx:06d}"
            episode_dir.mkdir(exist_ok=True)

            # 保存步骤数据
            steps_dir = episode_dir / "steps"
            steps_dir.mkdir(exist_ok=True)

            for step_idx, step_data in enumerate(episode['steps']):
                step_dir = steps_dir / str(step_idx)
                step_dir.mkdir(exist_ok=True)

                # 保存观测数据
                obs_dir = step_dir / "observation"
                obs_dir.mkdir(exist_ok=True)

                # 保存图像
                if 'wrist_rgb' in step_data['observation']:
                    img_path = obs_dir / "wrist_rgb.png"
                    Image.fromarray(step_data['observation']['wrist_rgb']).save(img_path)

                if 'left_shoulder_rgb' in step_data['observation']:
                    img_path = obs_dir / "left_shoulder_rgb.png"
                    Image.fromarray(step_data['observation']['left_shoulder_rgb']).save(img_path)

                # 保存状态向量
                if 'state' in step_data['observation']:
                    state_path = obs_dir / "state.npy"
                    np.save(state_path, step_data['observation']['state'])

                # 保存动作
                action_path = step_dir / "action.npy"
                np.save(action_path, step_data['action'])

                # 保存元数据
                metadata = {
                    'reward': step_data['reward'],
                    'is_terminal': step_data['is_terminal'],
                    'is_first': step_data['is_first'],
                    'task_description': step_data['task_description']
                }

                metadata_path = step_dir / "metadata.json"
                with open(metadata_path, 'w') as f:
                    json.dump(metadata, f, indent=2)

            # 保存episode元数据
            episode_metadata = {
                'episode_length': episode['episode_length'],
                'task_name': episode['task_name']
            }

            episode_metadata_path = episode_dir / "metadata.json"
            with open(episode_metadata_path, 'w') as f:
                json.dump(episode_metadata, f, indent=2)

    def convert_task(self, task_name: str):
        """转换指定任务的数据"""
        print(f"\n=== 转换任务 {task_name} ===")

        # 加载原始数据
        demos = self.load_demos(task_name)

        # 处理所有demonstration
        processed_episodes = []
        for demo_idx, demo in enumerate(demos):
            try:
                episode = self.process_demo(demo, task_name)
                processed_episodes.append(episode)

                if (demo_idx + 1) % 10 == 0:
                    print(f"已处理 {demo_idx + 1}/{len(demos)} 个示教")

            except Exception as e:
                print(f"处理示教 {demo_idx} 时出错: {e}")
                continue

        # 分割训练和验证数据
        train_episodes, val_episodes = self.split_demos(processed_episodes, self.config.train_split)

        print(f"训练集: {len(train_episodes)} 个episode")
        print(f"验证集: {len(val_episodes)} 个episode")

        # 保存数据集
        if train_episodes:
            self.save_rlds_dataset(train_episodes, "train", task_name)

        if val_episodes:
            self.save_rlds_dataset(val_episodes, "validation", task_name)

        return len(processed_episodes)

    def compute_and_save_statistics(self):
        """计算并保存动作统计信息"""
        if not self.action_stats.actions:
            print("警告: 没有动作数据用于计算统计信息")
            return

        stats = self.action_stats.compute_statistics()

        # 转换numpy数组为列表以便JSON序列化
        json_stats = {}
        for key, value in stats.items():
            if isinstance(value, np.ndarray):
                json_stats[key] = value.tolist()
            else:
                json_stats[key] = value

        # 保存统计信息
        stats_file = Path(self.config.output_dir) / "action_statistics.json"
        with open(stats_file, 'w') as f:
            json.dump(json_stats, f, indent=2)

        print(f"\n动作统计信息:")
        print(f"  总动作数: {stats['count']}")
        print(f"  均值: {stats['mean']}")
        print(f"  标准差: {stats['std']}")
        print(f"  范围: [{stats['min']}, {stats['max']}]")

        return stats

    def convert_all_tasks(self):
        """转换所有任务的数据"""
        print("开始转换RLBench数据到SpatialVLA格式...")
        print(f"输入目录: {self.config.input_dir}")
        print(f"输出目录: {self.config.output_dir}")
        print(f"任务列表: {self.config.task_names}")

        total_episodes = 0
        successful_tasks = 0

        for task_name in self.config.task_names:
            try:
                num_episodes = self.convert_task(task_name)
                total_episodes += num_episodes
                successful_tasks += 1

            except Exception as e:
                print(f"转换任务 {task_name} 失败: {e}")
                continue

        # 计算和保存统计信息
        if self.config.compute_stats:
            self.compute_and_save_statistics()

        # 保存数据集信息
        dataset_info = {
            'name': 'rlbench_spatialvla',
            'version': '1.0.0',
            'description': 'RLBench demonstrations converted for SpatialVLA training',
            'tasks': self.config.task_names,
            'total_episodes': total_episodes,
            'successful_tasks': successful_tasks,
            'train_split': self.config.train_split,
            'image_size': SPATIALVLA_IMAGE_SIZE,
            'action_dim': ACTION_DIM
        }

        info_file = Path(self.config.output_dir) / "dataset_info.json"
        with open(info_file, 'w') as f:
            json.dump(dataset_info, f, indent=2)

        print(f"\n=== 转换完成 ===")
        print(f"成功转换任务数: {successful_tasks}/{len(self.config.task_names)}")
        print(f"总episode数: {total_episodes}")
        print(f"数据已保存到: {self.config.output_dir}")

        return dataset_info

class DataValidator:
    """数据验证器"""

    @staticmethod
    def validate_episode(episode_dir: Path) -> bool:
        """验证单个episode的数据完整性"""
        try:
            # 检查元数据文件
            metadata_file = episode_dir / "metadata.json"
            if not metadata_file.exists():
                return False

            with open(metadata_file, 'r') as f:
                metadata = json.load(f)

            episode_length = metadata.get('episode_length', 0)
            if episode_length <= 0:
                return False

            # 检查步骤目录
            steps_dir = episode_dir / "steps"
            if not steps_dir.exists():
                return False

            # 验证每个步骤
            for step_idx in range(episode_length):
                step_dir = steps_dir / str(step_idx)
                if not step_dir.exists():
                    return False

                # 检查必要文件
                required_files = ["action.npy", "metadata.json"]
                for file_name in required_files:
                    if not (step_dir / file_name).exists():
                        return False

                # 检查观测目录
                obs_dir = step_dir / "observation"
                if not obs_dir.exists():
                    return False

            return True

        except Exception as e:
            print(f"验证episode {episode_dir} 时出错: {e}")
            return False

    @staticmethod
    def validate_dataset(dataset_dir: Path) -> Dict[str, Any]:
        """验证整个数据集"""
        validation_results = {
            'valid_episodes': 0,
            'invalid_episodes': 0,
            'tasks': {},
            'errors': []
        }

        for task_dir in dataset_dir.iterdir():
            if not task_dir.is_dir():
                continue

            task_name = task_dir.name
            task_results = {'train': 0, 'validation': 0, 'invalid': 0}

            for split in ['train', 'validation']:
                split_dir = task_dir / split
                if not split_dir.exists():
                    continue

                for episode_dir in split_dir.iterdir():
                    if not episode_dir.is_dir():
                        continue

                    if DataValidator.validate_episode(episode_dir):
                        task_results[split] += 1
                        validation_results['valid_episodes'] += 1
                    else:
                        task_results['invalid'] += 1
                        validation_results['invalid_episodes'] += 1
                        validation_results['errors'].append(f"Invalid episode: {episode_dir}")

            validation_results['tasks'][task_name] = task_results

        return validation_results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="转换RLBench数据到SpatialVLA格式")
    parser.add_argument('--input-dir', type=str, required=True,
                       help='RLBench原始数据目录')
    parser.add_argument('--output-dir', type=str, required=True,
                       help='输出RLDS数据集目录')
    parser.add_argument('--tasks', nargs='+', required=True,
                       help='要转换的任务列表')
    parser.add_argument('--train-split', type=float, default=0.8,
                       help='训练集比例')
    parser.add_argument('--no-stats', action='store_true',
                       help='跳过统计信息计算')
    parser.add_argument('--validate', action='store_true',
                       help='验证转换后的数据')

    args = parser.parse_args()

    # 创建配置
    config = ConversionConfig(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        task_names=args.tasks,
        train_split=args.train_split,
        compute_stats=not args.no_stats,
        validate_output=args.validate
    )

    # 创建输出目录
    Path(config.output_dir).mkdir(parents=True, exist_ok=True)

    try:
        # 执行转换
        builder = RLDSDatasetBuilder(config)
        dataset_info = builder.convert_all_tasks()

        # 验证数据（如果需要）
        if config.validate_output:
            print("\n验证转换后的数据...")
            validation_results = DataValidator.validate_dataset(Path(config.output_dir))

            print(f"验证结果:")
            print(f"  有效episodes: {validation_results['valid_episodes']}")
            print(f"  无效episodes: {validation_results['invalid_episodes']}")

            if validation_results['errors']:
                print("错误:")
                for error in validation_results['errors'][:10]:  # 只显示前10个错误
                    print(f"  - {error}")

        print(f"\n转换成功完成！数据保存在: {config.output_dir}")
        return 0

    except Exception as e:
        print(f"转换过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
"""
RLBench示教数据生成脚本

该脚本从RLBench环境生成高质量的示教数据，确保与SpatialVLA模型兼容。
主要功能：
1. 配置RLBench环境以匹配SpatialVLA需求
2. 生成指定任务的示教数据
3. 验证数据质量和格式兼容性
4. 保存为标准格式供后续处理

作者：SpatialVLA Integration Team
日期：2024
"""

import os
import sys
import json
import pickle
import argparse
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass

# RLBench imports
from rlbench.environment import Environment
from rlbench.observation_config import ObservationConfig, CameraConfig
from rlbench.action_modes.action_mode import MoveArmThenGripper
from rlbench.action_modes.arm_action_modes import EndEffectorPoseViaIK
from rlbench.action_modes.gripper_action_modes import Discrete
from rlbench.tasks import *

# 配置常量
SPATIALVLA_IMAGE_SIZE = (224, 224)
REQUIRED_ACTION_DIM = 7
DEFAULT_NUM_DEMOS = 100

# 适合空间推理的任务列表
SPATIAL_REASONING_TASKS = [
    'stack_blocks',
    'put_item_in_drawer',
    'place_shape_in_shape_sorter',
    'insert_onto_square_peg',
    'put_bottle_in_fridge',
    'reach_target',
    'pick_and_lift',
    'push_button',
    'open_drawer',
    'close_jar'
]

@dataclass
class DemoConfig:
    """示教数据生成配置"""
    task_names: List[str]
    num_demos_per_task: int
    output_dir: str
    headless: bool = True
    validate_data: bool = True
    save_images: bool = True
    save_depth: bool = False

class RLBenchDemoGenerator:
    """RLBench示教数据生成器"""

    def __init__(self, config: DemoConfig):
        self.config = config
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 初始化环境配置
        self.obs_config = self._create_observation_config()
        self.action_mode = self._create_action_mode()

        # 统计信息
        self.stats = {
            'total_demos': 0,
            'successful_demos': 0,
            'failed_demos': 0,
            'task_stats': {}
        }

    def _create_observation_config(self) -> ObservationConfig:
        """创建与SpatialVLA兼容的观测配置"""
        # 相机配置 - 匹配SpatialVLA输入要求
        camera_config = CameraConfig(
            rgb=True,
            depth=self.config.save_depth,
            point_cloud=False,
            mask=False,
            image_size=SPATIALVLA_IMAGE_SIZE,  # 关键：224x224
            depth_in_meters=True
        )

        obs_config = ObservationConfig(
            # 主要视角：腕部和左肩相机
            wrist_camera=camera_config,
            left_shoulder_camera=camera_config,

            # 禁用其他视角以节省计算
            right_shoulder_camera=CameraConfig(rgb=False, depth=False, point_cloud=False, mask=False),
            overhead_camera=CameraConfig(rgb=False, depth=False, point_cloud=False, mask=False),
            front_camera=CameraConfig(rgb=False, depth=False, point_cloud=False, mask=False),

            # 本体感知数据 - SpatialVLA可以使用
            joint_positions=True,
            joint_velocities=True,
            gripper_pose=True,
            gripper_open=True,

            # 禁用不需要的数据
            joint_forces=False,
            gripper_matrix=False,
            gripper_joint_positions=False,
            gripper_touch_forces=False,
            task_low_dim_state=False
        )

        return obs_config

    def _create_action_mode(self) -> MoveArmThenGripper:
        """创建与SpatialVLA兼容的动作模式"""
        # 关键：使用增量模式的EndEffectorPose
        arm_action_mode = EndEffectorPoseViaIK(
            absolute_mode=False,  # 增量模式，匹配SpatialVLA
            collision_checking=False  # 禁用碰撞检测以提高速度
        )

        gripper_action_mode = Discrete()  # 离散夹爪控制

        return MoveArmThenGripper(
            arm_action_mode=arm_action_mode,
            gripper_action_mode=gripper_action_mode
        )

    def _get_task_class(self, task_name: str):
        """根据任务名获取任务类"""
        try:
            # 转换任务名为类名
            class_name = ''.join([w.capitalize() for w in task_name.split('_')])
            task_class = globals().get(class_name)

            if task_class is None:
                raise ValueError(f"Task class {class_name} not found")

            return task_class

        except Exception as e:
            raise RuntimeError(f"Failed to load task {task_name}: {e}")

    def _validate_observation(self, obs) -> bool:
        """验证观测数据与SpatialVLA兼容性"""
        try:
            # 检查图像尺寸
            if hasattr(obs, 'wrist_rgb') and obs.wrist_rgb is not None:
                if obs.wrist_rgb.shape[:2] != SPATIALVLA_IMAGE_SIZE:
                    print(f"Warning: Wrist image size {obs.wrist_rgb.shape[:2]} != {SPATIALVLA_IMAGE_SIZE}")
                    return False

            if hasattr(obs, 'left_shoulder_rgb') and obs.left_shoulder_rgb is not None:
                if obs.left_shoulder_rgb.shape[:2] != SPATIALVLA_IMAGE_SIZE:
                    print(f"Warning: Shoulder image size {obs.left_shoulder_rgb.shape[:2]} != {SPATIALVLA_IMAGE_SIZE}")
                    return False

            # 检查必要的状态数据
            required_attrs = ['gripper_pose', 'gripper_open']
            for attr in required_attrs:
                if not hasattr(obs, attr):
                    print(f"Warning: Missing required attribute {attr}")
                    return False

            # 检查夹爪位姿维度
            if len(obs.gripper_pose) != 7:
                print(f"Warning: Gripper pose dimension {len(obs.gripper_pose)} != 7")
                return False

            return True

        except Exception as e:
            print(f"Observation validation error: {e}")
            return False

    def _validate_demo(self, demo: List) -> bool:
        """验证整个demonstration的质量"""
        if len(demo) < 2:
            print("Warning: Demo too short (< 2 steps)")
            return False

        # 验证每个观测
        for i, obs in enumerate(demo):
            if not self._validate_observation(obs):
                print(f"Warning: Invalid observation at step {i}")
                return False

        return True

    def generate_task_demos(self, task_name: str) -> Dict[str, Any]:
        """为指定任务生成示教数据"""
        print(f"\n=== 生成任务 {task_name} 的示教数据 ===")

        # 获取任务类
        task_class = self._get_task_class(task_name)

        # 创建环境
        env = Environment(
            action_mode=self.action_mode,
            obs_config=self.obs_config,
            headless=self.config.headless
        )

        try:
            env.launch()
            task = env.get_task(task_class)

            # 生成示教数据
            print(f"正在生成 {self.config.num_demos_per_task} 个示教...")
            demos = task.get_demos(
                amount=self.config.num_demos_per_task,
                live_demos=True  # 关键：使用实时生成确保格式一致
            )

            # 验证数据质量
            valid_demos = []
            for i, demo in enumerate(demos):
                if self.config.validate_data:
                    if self._validate_demo(demo):
                        valid_demos.append(demo)
                        print(f"✓ Demo {i+1}/{len(demos)} 验证通过")
                    else:
                        print(f"✗ Demo {i+1}/{len(demos)} 验证失败")
                        self.stats['failed_demos'] += 1
                else:
                    valid_demos.append(demo)

            # 保存数据
            output_file = self.output_dir / f"raw_demos_{task_name}.pkl"
            with open(output_file, 'wb') as f:
                pickle.dump(valid_demos, f)

            # 保存任务描述
            task_descriptions = task.get_task_descriptions()
            desc_file = self.output_dir / f"task_descriptions_{task_name}.json"
            with open(desc_file, 'w') as f:
                json.dump(task_descriptions, f, indent=2)

            # 更新统计信息
            task_stats = {
                'total_demos': len(demos),
                'valid_demos': len(valid_demos),
                'success_rate': len(valid_demos) / len(demos) if demos else 0,
                'avg_episode_length': np.mean([len(demo) for demo in valid_demos]) if valid_demos else 0
            }

            self.stats['task_stats'][task_name] = task_stats
            self.stats['total_demos'] += len(demos)
            self.stats['successful_demos'] += len(valid_demos)

            print(f"任务 {task_name} 完成:")
            print(f"  - 总示教数: {task_stats['total_demos']}")
            print(f"  - 有效示教数: {task_stats['valid_demos']}")
            print(f"  - 成功率: {task_stats['success_rate']:.2%}")
            print(f"  - 平均长度: {task_stats['avg_episode_length']:.1f} 步")

            return task_stats

        except Exception as e:
            print(f"生成任务 {task_name} 数据时出错: {e}")
            self.stats['failed_demos'] += self.config.num_demos_per_task
            return {'error': str(e)}

        finally:
            env.shutdown()

    def generate_all_demos(self) -> Dict[str, Any]:
        """生成所有任务的示教数据"""
        print("开始生成RLBench示教数据...")
        print(f"任务列表: {self.config.task_names}")
        print(f"每任务示教数: {self.config.num_demos_per_task}")
        print(f"输出目录: {self.config.output_dir}")

        # 保存配置
        config_file = self.output_dir / "generation_config.json"
        with open(config_file, 'w') as f:
            json.dump({
                'task_names': self.config.task_names,
                'num_demos_per_task': self.config.num_demos_per_task,
                'image_size': SPATIALVLA_IMAGE_SIZE,
                'headless': self.config.headless,
                'validate_data': self.config.validate_data
            }, f, indent=2)

        # 逐个生成任务数据
        for task_name in self.config.task_names:
            try:
                self.generate_task_demos(task_name)
            except KeyboardInterrupt:
                print("\n用户中断，正在保存已生成的数据...")
                break
            except Exception as e:
                print(f"任务 {task_name} 生成失败: {e}")
                continue

        # 保存最终统计信息
        stats_file = self.output_dir / "generation_stats.json"
        with open(stats_file, 'w') as f:
            json.dump(self.stats, f, indent=2)

        # 打印总结
        print("\n=== 数据生成完成 ===")
        print(f"总示教数: {self.stats['total_demos']}")
        print(f"成功示教数: {self.stats['successful_demos']}")
        print(f"失败示教数: {self.stats['failed_demos']}")
        print(f"总体成功率: {self.stats['successful_demos']/self.stats['total_demos']:.2%}")

        return self.stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成RLBench示教数据")
    parser.add_argument('--tasks', nargs='+', default=SPATIAL_REASONING_TASKS[:5],
                       help='要生成数据的任务列表')
    parser.add_argument('--num-demos', type=int, default=DEFAULT_NUM_DEMOS,
                       help='每个任务生成的示教数量')
    parser.add_argument('--output-dir', type=str, default='./rlbench_demos',
                       help='输出目录')
    parser.add_argument('--headless', action='store_true', default=True,
                       help='无头模式运行')
    parser.add_argument('--no-validate', action='store_true',
                       help='跳过数据验证')
    parser.add_argument('--save-depth', action='store_true',
                       help='保存深度图像')

    args = parser.parse_args()

    # 创建配置
    config = DemoConfig(
        task_names=args.tasks,
        num_demos_per_task=args.num_demos,
        output_dir=args.output_dir,
        headless=args.headless,
        validate_data=not args.no_validate,
        save_depth=args.save_depth
    )

    # 创建生成器并运行
    generator = RLBenchDemoGenerator(config)

    try:
        stats = generator.generate_all_demos()
        print(f"\n数据已保存到: {config.output_dir}")
        return 0

    except Exception as e:
        print(f"生成过程中出现错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
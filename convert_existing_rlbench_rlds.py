#!/usr/bin/env python3
"""
现有RLBench RLDS数据转换脚本

该脚本专门处理已有的RLBench RLDS格式数据，转换为SpatialVLA训练所需的格式。
输入：/media/liuzhuoyang/data/rlbench/rlds/6tasks_selected_keyframe_nextpc_0806
输出：SpatialVLA兼容的数据集配置

主要功能：
1. 分析现有RLDS数据的结构和格式
2. 创建SpatialVLA数据配置文件
3. 验证数据兼容性
4. 生成训练配置

作者：SpatialVLA Integration Team
日期：2024
"""

import os
import sys
import json
import argparse
import numpy as np
import tensorflow as tf
import tensorflow_datasets as tfds
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import dlimp as dl

# 添加项目路径
sys.path.append(str(Path(__file__).parent))
from data.rlds import make_dataset_from_rlds
from data.utils.data_utils import get_dataset_statistics, NormalizationType

@dataclass
class RLBenchRLDSConfig:
    """RLBench RLDS数据配置"""
    data_path: str
    dataset_name: str = "rlbench"
    version: str = "1.0.0"
    output_dir: str = "./rlbench_spatialvla_config"

    # SpatialVLA兼容性配置
    image_size: int = 224
    train_split_ratio: float = 0.9

    # 数据处理配置
    compute_statistics: bool = True
    validate_format: bool = True

class RLBenchRLDSAnalyzer:
    """RLBench RLDS数据分析器"""

    def __init__(self, config: RLBenchRLDSConfig):
        self.config = config
        self.data_path = Path(config.data_path)
        self.dataset_path = self.data_path / config.dataset_name / config.version

        # 验证数据路径
        if not self.dataset_path.exists():
            raise FileNotFoundError(f"Dataset path not found: {self.dataset_path}")

    def analyze_dataset_structure(self) -> Dict[str, Any]:
        """分析数据集结构"""
        print(f"Analyzing dataset: {self.dataset_path}")

        # 读取数据集信息
        dataset_info_file = self.dataset_path / "dataset_info.json"
        features_file = self.dataset_path / "features.json"

        analysis = {
            "dataset_path": str(self.dataset_path),
            "files": list(self.dataset_path.glob("*")),
            "has_dataset_info": dataset_info_file.exists(),
            "has_features": features_file.exists(),
        }

        # 读取数据集信息
        if dataset_info_file.exists():
            with open(dataset_info_file, 'r') as f:
                dataset_info = json.load(f)
            analysis["dataset_info"] = dataset_info
            print(f"Dataset name: {dataset_info.get('name', 'Unknown')}")
            print(f"Dataset version: {dataset_info.get('version', 'Unknown')}")
            print(f"Dataset description: {dataset_info.get('description', 'No description')}")

        # 读取特征信息
        if features_file.exists():
            with open(features_file, 'r') as f:
                features = json.load(f)
            analysis["features"] = features
            print(f"Feature structure: {list(features.keys())}")

        return analysis

    def load_sample_data(self, num_samples: int = 5) -> List[Dict]:
        """加载样本数据进行分析"""
        print(f"Loading {num_samples} samples for analysis...")

        try:
            # 使用tfds加载数据
            builder = tfds.builder_from_directory(str(self.dataset_path))

            # 获取数据集信息
            print(f"Dataset info: {builder.info}")
            print(f"Available splits: {list(builder.info.splits.keys())}")

            # 加载训练数据样本
            split_name = "train" if "train" in builder.info.splits else list(builder.info.splits.keys())[0]
            dataset = builder.as_dataset(split=f"{split_name}[:{num_samples}]")

            samples = []
            for i, sample in enumerate(dataset):
                print(f"\n=== Sample {i+1} ===")
                sample_dict = self._analyze_sample(sample)
                samples.append(sample_dict)

                if i >= num_samples - 1:
                    break

            return samples

        except Exception as e:
            print(f"Error loading data: {e}")
            return []

    def _analyze_sample(self, sample) -> Dict[str, Any]:
        """分析单个样本的结构"""
        def analyze_tensor(tensor, name=""):
            if isinstance(tensor, tf.Tensor):
                return {
                    "shape": tensor.shape.as_list(),
                    "dtype": str(tensor.dtype),
                    "name": name
                }
            elif isinstance(tensor, dict):
                return {key: analyze_tensor(value, f"{name}.{key}") for key, value in tensor.items()}
            else:
                return {"type": type(tensor).__name__, "value": str(tensor)[:100]}

        analysis = analyze_tensor(sample)

        # 打印关键信息
        if "steps" in sample:
            print(f"  Episode length: {len(sample['steps'])}")
            if len(sample['steps']) > 0:
                step = sample['steps'][0]
                if "observation" in step:
                    obs = step["observation"]
                    print(f"  Observation keys: {list(obs.keys()) if isinstance(obs, dict) else 'Not a dict'}")

                    # 分析图像
                    for key in obs.keys() if isinstance(obs, dict) else []:
                        if "image" in key.lower() or "rgb" in key.lower():
                            img_tensor = obs[key]
                            if hasattr(img_tensor, 'shape'):
                                print(f"    {key}: {img_tensor.shape} {img_tensor.dtype}")

                    # 分析状态
                    if "state" in obs or "proprio" in obs:
                        state_key = "state" if "state" in obs else "proprio"
                        state_tensor = obs[state_key]
                        if hasattr(state_tensor, 'shape'):
                            print(f"    {state_key}: {state_tensor.shape} {state_tensor.dtype}")

                if "action" in step:
                    action = step["action"]
                    if hasattr(action, 'shape'):
                        print(f"  Action: {action.shape} {action.dtype}")

                if "language_instruction" in step:
                    instruction = step["language_instruction"]
                    print(f"  Language instruction: {instruction}")

        return analysis

    def infer_spatialvla_config(self, samples: List[Dict]) -> Dict[str, Any]:
        """根据样本数据推断SpatialVLA配置"""
        print("\nInferring SpatialVLA compatible configuration...")

        config = {
            "name": f"{self.config.dataset_name}/{self.config.version}",
            "data_dir": str(self.config.data_path),
            "image_obs_keys": {},
            "depth_obs_keys": {},
            "state_obs_keys": [],
            "language_key": None,
            "action_encoding": "EEF_POS",  # 默认末端执行器位置
            "state_encoding": "POS_QUAT",  # 默认位置+四元数
        }

        if not samples:
            print("Warning: No sample data available, using default configuration")
            return config

        # 分析第一个样本的结构
        try:
            # 这里需要根据实际的数据结构来推断
            # 由于我们无法直接访问数据，提供一个通用的推断逻辑

            # 常见的RLBench图像键名映射
            image_key_mapping = {
                "wrist_rgb": "wrist",
                "left_shoulder_rgb": "primary",
                "right_shoulder_rgb": "secondary",
                "overhead_rgb": "overhead",
                "front_rgb": "front"
            }

            depth_key_mapping = {
                "wrist_depth": "wrist",
                "left_shoulder_depth": "primary",
                "right_shoulder_depth": "secondary",
                "overhead_depth": "overhead",
                "front_depth": "front"
            }

            # 设置图像观测键
            config["image_obs_keys"] = {
                "primary": "left_shoulder_rgb",  # 主视角
                "secondary": None,  # 次要视角
                "wrist": "wrist_rgb"  # 腕部视角
            }

            # 设置深度观测键
            config["depth_obs_keys"] = {
                "primary": "left_shoulder_depth",
                "secondary": None,
                "wrist": "wrist_depth"
            }

            # 设置状态观测键
            config["state_obs_keys"] = ["proprio"]  # 通用本体感知状态

            # 设置语言键
            config["language_key"] = "language_instruction"

            print(f"Inferred configuration:")
            print(f"  Image keys: {config['image_obs_keys']}")
            print(f"  Depth keys: {config['depth_obs_keys']}")
            print(f"  State keys: {config['state_obs_keys']}")
            print(f"  Language key: {config['language_key']}")

        except Exception as e:
            print(f"Error inferring configuration: {e}")

        return config

    def create_dataset_config(self, spatialvla_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建完整的数据集配置"""
        print("\nCreating SpatialVLA dataset configuration...")

        # 基础配置
        dataset_config = {
            "name": spatialvla_config["name"],
            "data_dir": spatialvla_config["data_dir"],

            # 图像和深度配置
            "image_obs_keys": spatialvla_config["image_obs_keys"],
            "depth_obs_keys": spatialvla_config["depth_obs_keys"],
            "state_obs_keys": spatialvla_config["state_obs_keys"],
            "language_key": spatialvla_config["language_key"],

            # 动作和状态编码
            "action_encoding": spatialvla_config["action_encoding"],
            "state_encoding": spatialvla_config["state_encoding"],

            # 归一化配置
            "action_proprio_normalization_type": "BOUNDS_Q99",  # 使用99分位数归一化

            # 数据处理配置
            "shuffle": True,
            "shuffle_seed": 42,
            "num_parallel_reads": 8,
            "num_parallel_calls": 16,
        }

        return dataset_config

    def test_data_loading(self, dataset_config: Dict[str, Any]) -> bool:
        """测试数据加载是否正常"""
        print("\nTesting data loading...")

        try:
            # 尝试加载少量数据
            dataset, stats = make_dataset_from_rlds(
                name=dataset_config["name"],
                data_dir=dataset_config["data_dir"],
                train=True,
                shuffle_seed=42,
                image_obs_keys=dataset_config["image_obs_keys"],
                depth_obs_keys=dataset_config["depth_obs_keys"],
                state_obs_keys=dataset_config["state_obs_keys"],
                language_key=dataset_config["language_key"],
                action_proprio_normalization_type=getattr(NormalizationType, dataset_config["action_proprio_normalization_type"]),
                num_parallel_reads=1,
                num_parallel_calls=1,
            )

            # 尝试获取一个样本
            sample_count = 0
            for sample in dataset.iterator():
                print(f"Successfully loaded sample: {sample_count + 1}")

                # 分析样本结构
                if "observation" in sample:
                    obs = sample["observation"]
                    print(f"  Observation keys: {list(obs.keys())}")

                    # 检查图像
                    for key in ["image_primary", "image_wrist"]:
                        if key in obs:
                            img = obs[key]
                            print(f"    {key}: {img.shape} {img.dtype}")

                    # 检查状态
                    if "proprio" in obs:
                        proprio = obs["proprio"]
                        print(f"    proprio: {proprio.shape} {proprio.dtype}")

                if "action" in sample:
                    action = sample["action"]
                    print(f"  Action: {action.shape} {action.dtype}")

                if "task" in sample and "language_instruction" in sample["task"]:
                    instruction = sample["task"]["language_instruction"]
                    print(f"  Language instruction: {instruction}")

                sample_count += 1
                if sample_count >= 3:  # 只测试3个样本
                    break

            print(f"✓ Data loading test successful, tested {sample_count} samples")
            return True

        except Exception as e:
            print(f"✗ Data loading test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    def compute_dataset_statistics(self, dataset_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """计算数据集统计信息"""
        if not self.config.compute_statistics:
            return None

        print("\nComputing dataset statistics...")

        try:
            # 加载数据集
            dataset, _ = make_dataset_from_rlds(
                name=dataset_config["name"],
                data_dir=dataset_config["data_dir"],
                train=True,
                shuffle_seed=42,
                image_obs_keys=dataset_config["image_obs_keys"],
                depth_obs_keys=dataset_config["depth_obs_keys"],
                state_obs_keys=dataset_config["state_obs_keys"],
                language_key=dataset_config["language_key"],
                action_proprio_normalization_type=NormalizationType.NORMAL,  # 先不归一化
                num_parallel_reads=4,
                num_parallel_calls=8,
            )

            # 计算统计信息
            stats = get_dataset_statistics(
                dataset=dataset,
                hash_dependencies=[dataset_config["name"]],
                save_dir=self.config.output_dir
            )

            print("✓ Statistics computation completed")
            return stats

        except Exception as e:
            print(f"✗ Statistics computation failed: {e}")
            return None

    def save_configs(self, dataset_config: Dict[str, Any], stats: Optional[Dict[str, Any]] = None):
        """保存配置文件"""
        output_dir = Path(self.config.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 保存数据集配置
        config_file = output_dir / "rlbench_dataset_config.json"
        with open(config_file, 'w') as f:
            json.dump(dataset_config, f, indent=2, default=str)
        print(f"✓ Dataset configuration saved: {config_file}")

        # 保存统计信息
        if stats:
            stats_file = output_dir / "dataset_statistics.json"
            with open(stats_file, 'w') as f:
                json.dump(stats, f, indent=2, default=str)
            print(f"✓ Statistics saved: {stats_file}")

        # 创建训练配置模板
        self._create_training_config_template(output_dir, dataset_config)

    def _create_training_config_template(self, output_dir: Path, dataset_config: Dict[str, Any]):
        """创建训练配置模板"""
        training_config = {
            "data_mix": [
                {
                    "name": dataset_config["name"],
                    "data_dir": dataset_config["data_dir"],
                    "weight": 1.0,
                    "image_obs_keys": dataset_config["image_obs_keys"],
                    "depth_obs_keys": dataset_config["depth_obs_keys"],
                    "state_obs_keys": dataset_config["state_obs_keys"],
                    "language_key": dataset_config["language_key"],
                    "action_proprio_normalization_type": dataset_config["action_proprio_normalization_type"],
                }
            ],

            # 训练参数
            "model_name_or_path": "IPEC-COMMUNITY/spatialvla-4b-224-pt",
            "image_size": self.config.image_size,
            "max_seq_length": 1024,
            "batch_size": 8,
            "learning_rate": 1e-4,
            "num_train_epochs": 3,

            # LoRA配置
            "use_lora": True,
            "lora_r": 16,
            "lora_alpha": 32,
            "lora_target_modules": "all-linear",
            "modules_to_save": "action_head",

            # 其他配置
            "freeze_vision_tower": False,
            "freeze_llm_embed": True,
            "gradient_checkpointing": True,
            "dataloader_num_workers": 4,
        }

        config_file = output_dir / "training_config_template.json"
        with open(config_file, 'w') as f:
            json.dump(training_config, f, indent=2)
        print(f"✓ Training configuration template saved: {config_file}")

    def run_full_analysis(self) -> Dict[str, Any]:
        """运行完整的分析流程"""
        print("=" * 60)
        print("RLBench RLDS Data Analysis and Conversion")
        print("=" * 60)

        # 1. 分析数据集结构
        structure_analysis = self.analyze_dataset_structure()

        # 2. 加载样本数据
        samples = self.load_sample_data(num_samples=3)

        # 3. 推断SpatialVLA配置
        spatialvla_config = self.infer_spatialvla_config(samples)

        # 4. 创建数据集配置
        dataset_config = self.create_dataset_config(spatialvla_config)

        # 5. 测试数据加载
        loading_success = self.test_data_loading(dataset_config)

        # 6. 计算统计信息（可选）
        stats = None
        if loading_success and self.config.compute_statistics:
            stats = self.compute_dataset_statistics(dataset_config)

        # 7. 保存配置
        self.save_configs(dataset_config, stats)

        # 返回结果
        result = {
            "structure_analysis": structure_analysis,
            "dataset_config": dataset_config,
            "loading_success": loading_success,
            "statistics": stats,
            "output_dir": self.config.output_dir
        }

        print("\n" + "=" * 60)
        print("Analysis completed!")
        print(f"Configuration files saved to: {self.config.output_dir}")
        print("=" * 60)

        return result

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Analyze and convert existing RLBench RLDS data")
    parser.add_argument('--data-path', type=str,
                       default='/media/liuzhuoyang/data/rlbench/rlds/6tasks_selected_keyframe_nextpc_0806',
                       help='RLBench RLDS data path')
    parser.add_argument('--dataset-name', type=str, default='rlbench',
                       help='Dataset name')
    parser.add_argument('--version', type=str, default='1.0.0',
                       help='Dataset version')
    parser.add_argument('--output-dir', type=str, default='./rlbench_spatialvla_config',
                       help='Output configuration directory')
    parser.add_argument('--image-size', type=int, default=224,
                       help='Image size')
    parser.add_argument('--no-stats', action='store_true',
                       help='Skip statistics computation')
    parser.add_argument('--no-validate', action='store_true',
                       help='Skip data validation')

    args = parser.parse_args()

    # 创建配置
    config = RLBenchRLDSConfig(
        data_path=args.data_path,
        dataset_name=args.dataset_name,
        version=args.version,
        output_dir=args.output_dir,
        image_size=args.image_size,
        compute_statistics=not args.no_stats,
        validate_format=not args.no_validate
    )

    try:
        # 创建分析器
        analyzer = RLBenchRLDSAnalyzer(config)

        # 运行分析
        result = analyzer.run_full_analysis()

        # 输出使用指南
        print_usage_guide(result)

        return 0

    except Exception as e:
        print(f"Error occurred during analysis: {e}")
        import traceback
        traceback.print_exc()
        return 1

def print_usage_guide(result: Dict[str, Any]):
    """打印使用指南"""
    print("\n" + "=" * 60)
    print("Usage Guide")
    print("=" * 60)

    output_dir = result["output_dir"]

    print(f"\n1. Configuration file locations:")
    print(f"   - Dataset config: {output_dir}/rlbench_dataset_config.json")
    print(f"   - Training config template: {output_dir}/training_config_template.json")
    if result["statistics"]:
        print(f"   - Dataset statistics: {output_dir}/dataset_statistics.json")

    print(f"\n2. Usage in training scripts:")
    print(f"   ```python")
    print(f"   # Load configuration")
    print(f"   with open('{output_dir}/rlbench_dataset_config.json', 'r') as f:")
    print(f"       dataset_config = json.load(f)")
    print(f"   ")
    print(f"   # Use in data_mix")
    print(f"   data_mix = [dataset_config]")
    print(f"   ```")

    print(f"\n3. Training command example:")
    print(f"   ```bash")
    print(f"   python train.py \\")
    print(f"       --model_name_or_path IPEC-COMMUNITY/spatialvla-4b-224-pt \\")
    print(f"       --data_root_dir {result['dataset_config']['data_dir']} \\")
    print(f"       --data_mix_config {output_dir}/training_config_template.json \\")
    print(f"       --output_dir ./checkpoints/spatialvla_rlbench \\")
    print(f"       --num_train_epochs 3 \\")
    print(f"       --per_device_train_batch_size 8 \\")
    print(f"       --learning_rate 1e-4 \\")
    print(f"       --use_lora")
    print(f"   ```")

    print(f"\n4. Data validation:")
    if result["loading_success"]:
        print(f"   ✓ Data loading test passed")
    else:
        print(f"   ✗ Data loading test failed, please check configuration")

    print(f"\n5. Important notes:")
    print(f"   - Ensure image size is 224x224 to match SpatialVLA")
    print(f"   - Check that action dimension is 7D")
    print(f"   - Verify language instruction format")
    print(f"   - Adjust image_obs_keys and state_obs_keys based on actual data")

    print("\n" + "=" * 60)

if __name__ == "__main__":
    sys.exit(main())
  🎯 操作路线概览

  核心策略：使用SpatialVLA的lerobot-branch分支，通过LeRobot框架实现标准化的
  数据处理和模型微调流程。

  📋 具体实施步骤

  阶段1: 环境搭建与依赖安装

  # 1. 安装RLBench环境
  export COPPELIASIM_ROOT=${HOME}/CoppeliaSim
  export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$COPPELIASIM_ROOT
  export QT_QPA_PLATFORM_PLUGIN_PATH=$COPPELIASIM_ROOT

  wget https://downloads.coppeliarobotics.com/V4_1_0/CoppeliaSim_Edu_V4_1_0
  _Ubuntu20_04.tar.xz
  mkdir -p $COPPELIASIM_ROOT && tar -xf
  CoppeliaSim_Edu_V4_1_0_Ubuntu20_04.tar.xz -C $COPPELIASIM_ROOT
  --strip-components 1
  pip install git+https://github.com/stepjam/RLBench.git

  # 2. 克隆SpatialVLA的lerobot分支
  git clone https://github.com/SpatialVLA/SpatialVLA.git
  cd SpatialVLA
  git checkout lerobot-branch
  pip install -r requirements.txt

  阶段2: 示教数据生成

  # generate_demos.py
  import numpy as np
  from rlbench.action_modes.action_mode import MoveArmThenGripper
  from rlbench.action_modes.arm_action_modes import EndEffectorPose
  from rlbench.action_modes.gripper_action_modes import Discrete
  from rlbench.environment import Environment
  from rlbench.observation_config import ObservationConfig
  import pickle

  # 关键配置：确保与SpatialVLA兼容
  obs_config = ObservationConfig()
  obs_config.set_all(True)  # 启用所有观测类型
  obs_config.image_size = [224, 224]  # 匹配SpatialVLA输入尺寸

  action_mode = MoveArmThenGripper(
      arm_action_mode=EndEffectorPose(absolute_mode=False),  # 增量模式
      gripper_action_mode=Discrete()
  )

  env = Environment(action_mode, obs_config=obs_config)
  env.launch()

  # 选择适合空间推理的任务
  tasks = ['stack_blocks', 'put_item_in_drawer',
  'place_shape_in_shape_sorter',
           'insert_onto_square_peg', 'put_bottle_in_fridge']

  for task_name in tasks:
      task_class = getattr(__import__(f'rlbench.tasks.{task_name}',
  fromlist=[task_name]),
                           task_name.title().replace('_', ''))
      task = env.get_task(task_class)

      demos = task.get_demos(100, live_demos=True)  # 生成100个示教

      # 保存原始数据
      with open(f'raw_demos_{task_name}.pkl', 'wb') as f:
          pickle.dump(demos, f)

  阶段3: 数据转换为LeRobotDataset格式

  # convert_to_lerobot.py
  import pandas as pd
  import numpy as np
  from pathlib import Path
  import cv2
  import pickle
  import json

  def convert_rlbench_to_lerobot(demos, task_name, output_dir):
      """将RLBench演示数据转换为LeRobotDataset格式"""

      output_path = Path(output_dir) / task_name
      output_path.mkdir(parents=True, exist_ok=True)

      # 创建目录结构
      (output_path / "data").mkdir(exist_ok=True)
      (output_path / "videos").mkdir(exist_ok=True)
      (output_path / "meta").mkdir(exist_ok=True)

      all_episodes = []
      episode_data = []

      for ep_idx, demo in enumerate(demos):
          episode_length = len(demo)

          # 提取视频序列
          wrist_frames = []
          shoulder_frames = []

          episode_actions = []
          episode_states = []

          for step_idx, obs in enumerate(demo):
              # 提取图像
              wrist_frames.append(obs.wrist_rgb)
              shoulder_frames.append(obs.left_shoulder_rgb)

              # 提取状态和动作
              state = np.concatenate([
                  obs.gripper_pose,  # 7D: 位置+四元数
                  [obs.gripper_open]  # 1D: 夹爪状态
              ])
              episode_states.append(state)

              # 构建动作向量 (7D: ΔT[3] + ΔR[3] + G[1])
              if step_idx < len(demo) - 1:
                  next_obs = demo[step_idx + 1]
                  delta_pos = next_obs.gripper_pose[:3] -
  obs.gripper_pose[:3]
                  delta_rot = next_obs.gripper_pose[3:6] -
  obs.gripper_pose[3:6]
                  gripper_action = next_obs.gripper_open
                  action = np.concatenate([delta_pos, delta_rot,
  [gripper_action]])
              else:
                  action = np.zeros(7)  # 最后一步的动作

              episode_actions.append(action)

          # 保存视频
          save_video_sequence(wrist_frames, output_path / "videos" /
  f"wrist_rgb_episode_{ep_idx:04d}.mp4")
          save_video_sequence(shoulder_frames, output_path / "videos" /
  f"shoulder_rgb_episode_{ep_idx:04d}.mp4")

          # 构建episode数据
          for step_idx in range(episode_length):
              episode_data.append({
                  'episode_index': ep_idx,
                  'frame_index': step_idx,
                  'timestamp': step_idx * 0.1,  # 假设10Hz
                  'observation.state': episode_states[step_idx],
                  'action': episode_actions[step_idx],
                  'task': task_name
              })

          all_episodes.append({
              'episode_index': ep_idx,
              'length': episode_length,
              'task': task_name
          })

      # 保存数据表
      df = pd.DataFrame(episode_data)
      df.to_parquet(output_path / "data" / "train.parquet", index=False)

      # 保存元数据
      info = {
          'total_episodes': len(demos),
          'total_frames': len(episode_data),
          'fps': 10,
          'features': {
              'observation.state': {'dtype': 'float32', 'shape': [8]},
              'action': {'dtype': 'float32', 'shape': [7]}
          }
      }

      with open(output_path / "meta" / "info.json", 'w') as f:
          json.dump(info, f, indent=2)

      with open(output_path / "meta" / "episodes.jsonl", 'w') as f:
          for ep in all_episodes:
              f.write(json.dumps(ep) + '\n')

  def save_video_sequence(frames, output_path):
      """保存图像序列为MP4视频"""
      if not frames:
          return

      height, width = frames[0].shape[:2]
      fourcc = cv2.VideoWriter_fourcc(*'mp4v')
      out = cv2.VideoWriter(str(output_path), fourcc, 10.0, (width,
  height))

      for frame in frames:
          frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
          out.write(frame_bgr)

      out.release()

  # 使用示例
  for task_name in tasks:
      with open(f'raw_demos_{task_name}.pkl', 'rb') as f:
          demos = pickle.load(f)
      convert_rlbench_to_lerobot(demos, task_name, './lerobot_datasets')

  阶段4: 模型微调配置

  # configs/train_spatialvla_rlbench.yaml
  # LeRobot训练配置文件

  env:
    name: rlbench_spatialvla

  dataset:
    name: custom_rlbench
    path: ./lerobot_datasets

  model:
    name: spatialvla
    pretrained_model_name_or_path: "IPEC-COMMUNITY/spatialvla-4b-224-pt"

  training:
    batch_size: 8
    learning_rate: 1e-5
    num_epochs: 50
    use_lora: true  # 使用LoRA进行高效微调
    lora_rank: 16
    lora_alpha: 32

  eval:
    n_episodes: 10
    eval_freq: 5

  阶段5: 执行微调训练

  # 使用lerobot框架进行训练
  cd SpatialVLA  # 确保在lerobot-branch分支
  python train_policy.py --config configs/train_spatialvla_rlbench.yaml

  阶段6: 在线评估脚本

  # evaluate_model.py
  import torch
  from transformers import AutoModel, AutoProcessor
  from rlbench.environment import Environment
  from rlbench.action_modes.action_mode import MoveArmThenGripper
  from rlbench.action_modes.arm_action_modes import EndEffectorPose
  from rlbench.action_modes.gripper_action_modes import Discrete
  import numpy as np

  class SpatialVLAEvaluator:
      def __init__(self, model_path, device='cuda'):
          self.processor = AutoProcessor.from_pretrained(model_path,
  trust_remote_code=True)
          self.model = AutoModel.from_pretrained(
              model_path, trust_remote_code=True,
  torch_dtype=torch.bfloat16
          ).eval().to(device)
          self.device = device

      def evaluate_task(self, task_class, task_description,
  num_episodes=10):
          """评估特定任务的性能"""

          # 设置环境
          action_mode = MoveArmThenGripper(
              arm_action_mode=EndEffectorPose(absolute_mode=False),
              gripper_action_mode=Discrete()
          )
          env = Environment(action_mode)
          env.launch()

          task = env.get_task(task_class)
          success_count = 0

          for episode in range(num_episodes):
              descriptions, obs = task.reset()

              max_steps = 200
              for step in range(max_steps):
                  # 预处理观测
                  image = obs.wrist_rgb

                  # 模型推理
                  inputs = self.processor(
                      images=[image],
                      text=task_description,
                      return_tensors="pt"
                  ).to(self.device)

                  with torch.no_grad():
                      action_tokens = self.model.predict_action(inputs)
                      action = self.processor.decode_actions(
                          action_tokens,
                          unnorm_key="bridge_orig/1.0.0"
                      )[0]

                  # 执行动作
                  obs, reward, terminate = task.step(action)

                  if terminate:
                      if reward > 0.5:  # 任务成功
                          success_count += 1
                      break

          env.shutdown()
          success_rate = success_count / num_episodes
          print(f"Task: {task_class.__name__}, Success Rate:
  {success_rate:.2%}")
          return success_rate

  # 评估使用示例
  evaluator =
  SpatialVLAEvaluator("./checkpoints/spatialvla_rlbench_finetuned")

  from rlbench.tasks import StackBlocks, PutItemInDrawer
  results = {
      'stack_blocks': evaluator.evaluate_task(StackBlocks, "Stack the
  blocks on top of each other"),
      'put_item_in_drawer': evaluator.evaluate_task(PutItemInDrawer, "Put
  the item in the drawer")
  }

  🔧 关键技术要点

  1. 数据兼容性：确保RLBench的观测数据格式与SpatialVLA的Ego3D编码需求匹配
  2.
  动作空间映射：将SpatialVLA的7维输出正确映射到RLBench的EndEffectorPose模式
  3. LeRobot标准化：利用lerobot-branch实现标准化的数据处理和训练流程
  4. 内存管理：使用tcmalloc优化大规模数据加载时的内存使用

  ⚠️ 注意事项

  - 确保RLBench环境配置正确，特别是CoppeliaSim的路径设置
  - 微调过程需要较大GPU资源（建议4-8块A100）
  - 数据转换阶段需要充足的存储空间用于视频文件
  - 评估时注意坐标系和单位的一致性


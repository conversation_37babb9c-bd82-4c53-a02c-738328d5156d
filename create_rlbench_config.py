#!/usr/bin/env python3
"""
RLBench数据配置生成脚本

基于您现有的RLBench RLDS数据，生成SpatialVLA训练所需的配置文件。
数据路径: /media/liuzhuoyang/data/rlbench/rlds/6tasks_selected_keyframe_nextpc_0806

主要功能：
1. 生成数据集配置文件
2. 创建训练配置模板
3. 提供使用指南

作者：SpatialVLA Integration Team
日期：2024
"""

import json
import argparse
from pathlib import Path
from typing import Dict, Any

def create_rlbench_dataset_config(data_path: str, output_dir: str) -> Dict[str, Any]:
    """创建RLBench数据集配置"""

    # 基于RLBench数据的标准配置
    config = {
        "name": "rlbench/1.0.0",
        "data_dir": data_path,

        # 图像观测配置 - 根据RLBench标准格式
        "image_obs_keys": {
            "primary": "left_shoulder_rgb",    # 主视角：左肩相机
            "secondary": None,                 # 次要视角：暂不使用
            "wrist": "wrist_rgb"              # 腕部视角：腕部相机
        },

        # 深度观测配置
        "depth_obs_keys": {
            "primary": "left_shoulder_depth",  # 主视角深度
            "secondary": None,                 # 次要视角深度：暂不使用
            "wrist": "wrist_depth"            # 腕部深度
        },

        # 状态观测配置
        "state_obs_keys": ["proprio"],        # 本体感知状态

        # 语言指令配置
        "language_key": "language_instruction",

        # 编码配置
        "action_encoding": "EEF_POS",         # 末端执行器位置
        "state_encoding": "POS_QUAT",        # 位置+四元数

        # 归一化配置
        "action_proprio_normalization_type": "BOUNDS_Q99",  # 99分位数归一化

        # 数据处理配置
        "shuffle": True,
        "shuffle_seed": 42,
        "num_parallel_reads": 8,
        "num_parallel_calls": 16,
    }

    return config

def create_training_config(dataset_config: Dict[str, Any], output_dir: str) -> Dict[str, Any]:
    """创建训练配置"""

    training_config = {
        # 数据混合配置
        "data_mix": [
            {
                **dataset_config,  # 包含所有数据集配置
                "weight": 1.0,     # 数据权重
            }
        ],

        # 模型配置
        "model_name_or_path": "IPEC-COMMUNITY/spatialvla-4b-224-pt",
        "image_size": 224,
        "max_seq_length": 1024,

        # 训练超参数
        "num_train_epochs": 3,
        "per_device_train_batch_size": 8,
        "per_device_eval_batch_size": 8,
        "learning_rate": 1e-4,
        "weight_decay": 0.01,
        "warmup_ratio": 0.1,

        # LoRA配置
        "use_lora": True,
        "lora_r": 16,
        "lora_alpha": 32,
        "lora_target_modules": "all-linear",
        "modules_to_save": "action_head",

        # 优化配置
        "freeze_vision_tower": False,
        "freeze_llm_embed": True,
        "gradient_checkpointing": True,
        "dataloader_num_workers": 4,
        "remove_unused_columns": False,

        # 保存和日志配置
        "save_strategy": "epoch",
        "save_total_limit": 3,
        "logging_steps": 10,
        "report_to": "tensorboard",

        # 评估配置
        "evaluation_strategy": "epoch",
        "eval_steps": 500,

        # 其他配置
        "seed": 42,
        "fp16": True,
        "gradient_accumulation_steps": 1,
    }

    return training_config

def create_data_mix_config(dataset_config: Dict[str, Any]) -> Dict[str, Any]:
    """创建数据混合配置（用于训练脚本）"""

    data_mix_config = {
        "datasets": [
            {
                "name": dataset_config["name"],
                "data_dir": dataset_config["data_dir"],
                "weight": 1.0,

                # 数据处理配置
                "image_obs_keys": dataset_config["image_obs_keys"],
                "depth_obs_keys": dataset_config["depth_obs_keys"],
                "state_obs_keys": dataset_config["state_obs_keys"],
                "language_key": dataset_config["language_key"],
                "action_proprio_normalization_type": dataset_config["action_proprio_normalization_type"],

                # 数据增强配置
                "traj_transform_kwargs": {
                    "backward_windows_size": 0,
                    "backward_delta": 1,
                    "forward_window_size": 0,
                    "skip_unlabeled": True,
                    "goal_relabeling_strategy": "uniform",
                },

                "frame_transform_kwargs": {
                    "resize_size": [224, 224],
                    "num_parallel_calls": 16,
                }
            }
        ]
    }

    return data_mix_config

def save_configs(dataset_config: Dict[str, Any], training_config: Dict[str, Any],
                data_mix_config: Dict[str, Any], output_dir: str):
    """保存所有配置文件"""

    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 保存数据集配置
    dataset_config_file = output_path / "rlbench_dataset_config.json"
    with open(dataset_config_file, 'w') as f:
        json.dump(dataset_config, f, indent=2)
    print(f"✓ 数据集配置已保存: {dataset_config_file}")

    # 保存训练配置
    training_config_file = output_path / "training_config.json"
    with open(training_config_file, 'w') as f:
        json.dump(training_config, f, indent=2)
    print(f"✓ 训练配置已保存: {training_config_file}")

    # 保存数据混合配置
    data_mix_config_file = output_path / "data_mix_config.json"
    with open(data_mix_config_file, 'w') as f:
        json.dump(data_mix_config, f, indent=2)
    print(f"✓ 数据混合配置已保存: {data_mix_config_file}")

    return {
        "dataset_config_file": str(dataset_config_file),
        "training_config_file": str(training_config_file),
        "data_mix_config_file": str(data_mix_config_file)
    }

def create_usage_guide(config_files: Dict[str, str], data_path: str, output_dir: str):
    """创建使用指南"""

    guide_content = f"""# RLBench + SpatialVLA 训练指南

## 数据配置

您的RLBench数据位于: `{data_path}`
配置文件已生成到: `{output_dir}`

## 配置文件说明

1. **数据集配置** (`rlbench_dataset_config.json`)
   - 定义了数据加载的基本参数
   - 包含图像、深度、状态观测的键名映射
   - 设置了数据归一化方式

2. **训练配置** (`training_config.json`)
   - 完整的训练超参数配置
   - LoRA微调设置
   - 优化器和学习率调度配置

3. **数据混合配置** (`data_mix_config.json`)
   - 用于多数据集混合训练
   - 包含数据增强和变换配置

## 使用方法

### 方法1: 使用现有训练脚本

```bash
# 假设您有一个train.py脚本
python train.py \\
    --config {config_files['training_config_file']} \\
    --data_root_dir {data_path} \\
    --output_dir ./checkpoints/spatialvla_rlbench
```

### 方法2: 在Python代码中使用

```python
import json
from data.dataset import build_datasets

# 加载配置
with open('{config_files['data_mix_config_file']}', 'r') as f:
    data_mix_config = json.load(f)

# 创建数据集
train_dataset, eval_dataset = build_datasets(
    data_args=data_mix_config,
    output_dir="./checkpoints",
    vla_processor=your_processor
)
```

### 方法3: 集成到现有数据流程

```python
from data.rlds import make_dataset_from_rlds

# 加载数据集配置
with open('{config_files['dataset_config_file']}', 'r') as f:
    config = json.load(f)

# 创建数据集
dataset, stats = make_dataset_from_rlds(
    name=config["name"],
    data_dir=config["data_dir"],
    train=True,
    shuffle_seed=42,
    image_obs_keys=config["image_obs_keys"],
    depth_obs_keys=config["depth_obs_keys"],
    state_obs_keys=config["state_obs_keys"],
    language_key=config["language_key"],
    action_proprio_normalization_type=config["action_proprio_normalization_type"]
)
```

## 重要注意事项

### 1. 数据格式验证
在开始训练前，请验证数据格式是否正确：

```python
# 测试数据加载
for sample in dataset.iterator():
    print("观测键:", list(sample["observation"].keys()))
    print("动作形状:", sample["action"].shape)
    print("语言指令:", sample["task"]["language_instruction"])
    break
```

### 2. 图像键名映射
根据您的实际数据，可能需要调整以下配置：

```json
"image_obs_keys": {{
    "primary": "left_shoulder_rgb",    # 主视角图像键名
    "secondary": null,                 # 次要视角（如果有）
    "wrist": "wrist_rgb"              # 腕部视角图像键名
}}
```

### 3. 状态观测配置
如果您的数据包含不同的状态信息，请调整：

```json
"state_obs_keys": ["proprio"]  # 根据实际数据调整
```

### 4. 动作维度检查
确保动作维度为7维（3维位置 + 3维旋转 + 1维夹爪）：

```python
# 检查动作维度
assert sample["action"].shape[-1] == 7, "动作维度必须为7"
```

## 故障排除

### 常见问题1: 键名不匹配
如果出现键名不匹配错误，请检查：
1. 实际数据中的图像键名
2. 状态观测的键名
3. 语言指令的键名

### 常见问题2: 数据类型错误
确保：
1. 图像数据为uint8类型，形状为(H, W, 3)
2. 动作数据为float32类型，形状为(7,)
3. 语言指令为字符串类型

### 常见问题3: 路径问题
确保：
1. 数据路径正确且可访问
2. 输出目录有写入权限
3. 配置文件路径正确

## 下一步

1. **验证配置**: 运行数据加载测试
2. **调整参数**: 根据实际情况调整配置
3. **开始训练**: 使用生成的配置开始训练
4. **监控训练**: 使用tensorboard监控训练过程

如有问题，请检查：
- 数据格式是否符合RLDS标准
- 图像尺寸是否为224x224
- 动作空间是否为7维
- 语言指令是否存在且格式正确
"""

    # 保存使用指南
    guide_file = Path(output_dir) / "USAGE_GUIDE.md"
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(guide_content)

    print(f"✓ 使用指南已保存: {guide_file}")
    return str(guide_file)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成RLBench数据的SpatialVLA配置")
    parser.add_argument('--data-path', type=str,
                       default='/media/liuzhuoyang/data/rlbench/rlds/6tasks_selected_keyframe_nextpc_0806',
                       help='RLBench RLDS数据路径')
    parser.add_argument('--output-dir', type=str, default='./rlbench_configs',
                       help='配置文件输出目录')

    args = parser.parse_args()

    print("=" * 60)
    print("RLBench + SpatialVLA 配置生成器")
    print("=" * 60)
    print(f"数据路径: {args.data_path}")
    print(f"输出目录: {args.output_dir}")

    try:
        # 1. 创建数据集配置
        print("\n1. 创建数据集配置...")
        dataset_config = create_rlbench_dataset_config(args.data_path, args.output_dir)

        # 2. 创建训练配置
        print("2. 创建训练配置...")
        training_config = create_training_config(dataset_config, args.output_dir)

        # 3. 创建数据混合配置
        print("3. 创建数据混合配置...")
        data_mix_config = create_data_mix_config(dataset_config)

        # 4. 保存所有配置
        print("4. 保存配置文件...")
        config_files = save_configs(dataset_config, training_config, data_mix_config, args.output_dir)

        # 5. 创建使用指南
        print("5. 创建使用指南...")
        guide_file = create_usage_guide(config_files, args.data_path, args.output_dir)

        print("\n" + "=" * 60)
        print("配置生成完成!")
        print("=" * 60)
        print(f"配置文件位置: {args.output_dir}")
        print(f"使用指南: {guide_file}")
        print("\n请查看使用指南了解如何使用这些配置文件进行训练。")

        return 0

    except Exception as e:
        print(f"配置生成失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
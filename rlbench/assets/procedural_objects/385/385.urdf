
<robot name="blob385">
  <link name="random_obj_385">
    <contact>
      <lateral_friction value="1.0"/>
      <rolling_friction value="0.0"/>
      <inertia_scaling value="3.0"/>
      <contact_cfm value="0.0"/>
      <contact_erp value="1.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.0709"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="0"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="385.obj" scale="0.0085 0.0085 0.0085"/>
      </geometry>
      <material name="blockmat">
        <color rgba="0.55 0.19 0.99 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="385_coll.obj" scale="0.0085 0.0085 0.0085"/>
      </geometry>
    </collision>
  </link>
</robot>

# SpatialVLA + RLBench 完整集成指南

本指南提供了将SpatialVLA与RLBench集成的完整实施方案，包括环境搭建、数据生成、模型训练和评估的详细步骤。

## 📋 目录

1. [环境搭建](#1-环境搭建)
2. [数据生成](#2-数据生成)
3. [数据转换](#3-数据转换)
4. [模型微调](#4-模型微调)
5. [在线评估](#5-在线评估)
6. [故障排除](#6-故障排除)
7. [最佳实践](#7-最佳实践)

## 1. 环境搭建

### 1.1 系统要求

- **操作系统**: Ubuntu 20.04+ 或 macOS 10.15+
- **Python**: 3.8+
- **GPU**: NVIDIA GPU with CUDA 11.8+ (推荐RTX 3090或更高)
- **内存**: 32GB+ RAM
- **存储**: 100GB+ 可用空间

### 1.2 安装CoppeliaSim

```bash
# 设置环境变量
export COPPELIASIM_ROOT=${HOME}/CoppeliaSim
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$COPPELIASIM_ROOT
export QT_QPA_PLATFORM_PLUGIN_PATH=$COPPELIASIM_ROOT

# 下载并安装CoppeliaSim
wget https://downloads.coppeliarobotics.com/V4_1_0/CoppeliaSim_Edu_V4_1_0_Ubuntu20_04.tar.xz
mkdir -p $COPPELIASIM_ROOT
tar -xf CoppeliaSim_Edu_V4_1_0_Ubuntu20_04.tar.xz -C $COPPELIASIM_ROOT --strip-components 1
rm CoppeliaSim_Edu_V4_1_0_Ubuntu20_04.tar.xz
```

### 1.3 安装Python依赖

```bash
# 创建虚拟环境
conda create -n spatialvla-rlbench python=3.9
conda activate spatialvla-rlbench

# 安装PyTorch (根据你的CUDA版本调整)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装RLBench
pip install git+https://github.com/stepjam/RLBench.git

# 安装SpatialVLA依赖
pip install transformers>=4.47.0
pip install accelerate
pip install peft
pip install datasets
pip install pillow
pip install opencv-python
pip install scipy
pip install numpy
pip install tqdm

# 安装其他依赖
pip install tensorflow  # 用于RLDS格式
pip install matplotlib  # 用于可视化
```

### 1.4 克隆SpatialVLA代码库

```bash
git clone https://github.com/SpatialVLA/SpatialVLA.git
cd SpatialVLA
pip install -e .
```

### 1.5 验证安装

```bash
# 测试RLBench安装
python -c "from rlbench.environment import Environment; print('RLBench安装成功')"

# 测试SpatialVLA安装
python -c "from model.modeling_spatialvla import SpatialVLAForConditionalGeneration; print('SpatialVLA安装成功')"
```

## 2. 数据生成

### 2.1 配置数据生成

使用提供的`generate_rlbench_demos.py`脚本生成示教数据：

```bash
# 生成默认任务的示教数据
python generate_rlbench_demos.py \
    --tasks stack_blocks put_item_in_drawer reach_target \
    --num-demos 100 \
    --output-dir ./rlbench_demos \
    --headless

# 生成更多任务的数据
python generate_rlbench_demos.py \
    --tasks stack_blocks put_item_in_drawer place_shape_in_shape_sorter \
            insert_onto_square_peg put_bottle_in_fridge reach_target \
            pick_and_lift push_button open_drawer close_jar \
    --num-demos 200 \
    --output-dir ./rlbench_demos_large \
    --headless \
    --save-depth
```

### 2.2 验证生成的数据

```bash
# 检查生成的文件
ls -la ./rlbench_demos/
# 应该看到:
# - raw_demos_*.pkl 文件
# - task_descriptions_*.json 文件
# - generation_config.json
# - generation_stats.json

# 查看统计信息
cat ./rlbench_demos/generation_stats.json
```

### 2.3 数据质量检查

```python
# 检查示教数据质量的Python脚本
import pickle
import numpy as np
from pathlib import Path

def check_demo_quality(demo_file):
    with open(demo_file, 'rb') as f:
        demos = pickle.load(f)

    print(f"任务: {demo_file.stem}")
    print(f"示教数量: {len(demos)}")

    lengths = [len(demo) for demo in demos]
    print(f"平均长度: {np.mean(lengths):.1f} ± {np.std(lengths):.1f}")
    print(f"长度范围: [{min(lengths)}, {max(lengths)}]")

    # 检查图像尺寸
    if demos and len(demos[0]) > 0:
        obs = demos[0][0]
        if hasattr(obs, 'wrist_rgb'):
            print(f"腕部图像尺寸: {obs.wrist_rgb.shape}")
        if hasattr(obs, 'left_shoulder_rgb'):
            print(f"肩部图像尺寸: {obs.left_shoulder_rgb.shape}")

    print("-" * 40)

# 检查所有任务
demo_dir = Path("./rlbench_demos")
for demo_file in demo_dir.glob("raw_demos_*.pkl"):
    check_demo_quality(demo_file)
```

## 3. 数据转换

### 3.1 转换为SpatialVLA格式

使用`convert_to_spatialvla_format.py`脚本转换数据：

```bash
# 基本转换
python convert_to_spatialvla_format.py \
    --input-dir /media/liuzhuoyang/data/rlbench/rlds/ \
    --output-dir ./spatialvla_dataset \
    --tasks 6tasks_selected_keyframe_nextpc_0806 \
    --train-split 0.9 \
    --validate

# 转换所有任务
python convert_to_spatialvla_format.py \
    --input-dir ./rlbench_demos_large \
    --output-dir ./spatialvla_dataset_large \
    --tasks stack_blocks put_item_in_drawer place_shape_in_shape_sorter \
            insert_onto_square_peg put_bottle_in_fridge reach_target \
            pick_and_lift push_button open_drawer close_jar \
    --train-split 0.8 \
    --validate
```

### 3.2 验证转换结果

```bash
# 检查转换后的数据结构
tree ./spatialvla_dataset -L 3

# 查看数据集信息
cat ./spatialvla_dataset/dataset_info.json

# 查看动作统计信息
cat ./spatialvla_dataset/action_statistics.json
```

### 3.3 数据集格式说明

转换后的数据集结构：
```
spatialvla_dataset/
├── dataset_info.json          # 数据集元信息
├── action_statistics.json     # 动作统计信息
├── stack_blocks/              # 任务目录
│   ├── train/                 # 训练集
│   │   ├── episode_000000/    # Episode目录
│   │   │   ├── metadata.json  # Episode元数据
│   │   │   └── steps/         # 步骤目录
│   │   │       ├── 0/         # 步骤0
│   │   │       │   ├── observation/
│   │   │       │   │   ├── wrist_rgb.png
│   │   │       │   │   ├── left_shoulder_rgb.png
│   │   │       │   │   └── state.npy
│   │   │       │   ├── action.npy
│   │   │       │   └── metadata.json
│   │   │       └── ...
│   │   └── ...
│   └── validation/            # 验证集
└── ...
```
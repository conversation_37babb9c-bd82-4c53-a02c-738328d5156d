#!/usr/bin/env python3
"""
RLBench数据格式检查脚本

检查您的RLBench RLDS数据的实际格式，以便正确配置键名映射。
"""

import os
import sys
import tensorflow as tf
import tensorflow_datasets as tfds
from pathlib import Path

def inspect_rlbench_data(data_path: str):
    """检查RLBench数据的实际格式"""

    print(f"检查数据路径: {data_path}")

    # 构建数据集路径
    dataset_path = Path(data_path) / "rlbench" / "1.0.0"

    if not dataset_path.exists():
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return False

    print(f"✅ 找到数据集: {dataset_path}")

    try:
        # 加载数据集
        builder = tfds.builder_from_directory(str(dataset_path))

        print(f"数据集名称: {builder.info.name}")
        print(f"数据集版本: {builder.info.version}")
        print(f"可用分割: {list(builder.info.splits.keys())}")

        # 获取一个样本
        split_name = "train" if "train" in builder.info.splits else list(builder.info.splits.keys())[0]
        dataset = builder.as_dataset(split=f"{split_name}[:1]")

        print(f"\n=== 样本数据结构分析 ===")

        for sample in dataset:
            print(f"顶层键: {list(sample.keys())}")

            if "steps" in sample:
                steps = sample["steps"]
                print(f"Episode长度: {len(steps)}")

                if len(steps) > 0:
                    step = steps[0]
                    print(f"Step键: {list(step.keys())}")

                    # 分析observation
                    if "observation" in step:
                        obs = step["observation"]
                        print(f"\n观测键: {list(obs.keys())}")

                        # 检查图像相关的键
                        image_keys = []
                        for key in obs.keys():
                            if any(img_word in key.lower() for img_word in ['image', 'rgb', 'camera']):
                                image_keys.append(key)
                                img_tensor = obs[key]
                                print(f"  📷 {key}: {img_tensor.shape} {img_tensor.dtype}")

                        # 检查深度相关的键
                        depth_keys = []
                        for key in obs.keys():
                            if 'depth' in key.lower():
                                depth_keys.append(key)
                                depth_tensor = obs[key]
                                print(f"  🔍 {key}: {depth_tensor.shape} {depth_tensor.dtype}")

                        # 检查状态相关的键
                        state_keys = []
                        for key in obs.keys():
                            if any(state_word in key.lower() for state_word in ['state', 'proprio', 'robot', 'joint', 'gripper']):
                                state_keys.append(key)
                                state_tensor = obs[key]
                                print(f"  🤖 {key}: {state_tensor.shape} {state_tensor.dtype}")

                    # 分析action
                    if "action" in step:
                        action = step["action"]
                        print(f"\n动作: {action.shape} {action.dtype}")
                        if len(action.shape) > 0:
                            print(f"  动作维度: {action.shape[-1]}")

                    # 分析语言指令
                    language_keys = []
                    for key in step.keys():
                        if any(lang_word in key.lower() for lang_word in ['language', 'instruction', 'text', 'task']):
                            language_keys.append(key)
                            lang_data = step[key]
                            print(f"  💬 {key}: {lang_data}")

                    print(f"\n=== 推荐配置 ===")
                    print("基于检查结果，建议的配置为:")

                    # 推荐图像配置
                    if image_keys:
                        print(f"\n图像观测键配置:")
                        primary_key = None
                        wrist_key = None

                        for key in image_keys:
                            if 'wrist' in key.lower():
                                wrist_key = key
                            elif 'shoulder' in key.lower() or 'left' in key.lower():
                                primary_key = key

                        if not primary_key and image_keys:
                            primary_key = image_keys[0]  # 使用第一个作为主视角

                        print(f'  "primary": "{primary_key}",')
                        print(f'  "secondary": None,')
                        print(f'  "wrist": "{wrist_key}",')

                    # 推荐深度配置
                    if depth_keys:
                        print(f"\n深度观测键配置:")
                        primary_depth = None
                        wrist_depth = None

                        for key in depth_keys:
                            if 'wrist' in key.lower():
                                wrist_depth = key
                            elif 'shoulder' in key.lower() or 'left' in key.lower():
                                primary_depth = key

                        if not primary_depth and depth_keys:
                            primary_depth = depth_keys[0]

                        print(f'  "primary": "{primary_depth}",')
                        print(f'  "secondary": None,')
                        print(f'  "wrist": "{wrist_depth}",')

                    # 推荐状态配置
                    if state_keys:
                        print(f"\n状态观测键配置:")
                        print(f'  "state_obs_keys": {state_keys},')

                    # 推荐语言配置
                    if language_keys:
                        print(f"\n语言指令键配置:")
                        print(f'  "language_key": "{language_keys[0]}",')

            break  # 只分析第一个样本

        return True

    except Exception as e:
        print(f"❌ 检查数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    data_path = "/media/liuzhuoyang/data/rlbench/rlds/6tasks_selected_keyframe_nextpc_0806"

    print("=" * 60)
    print("RLBench数据格式检查")
    print("=" * 60)

    success = inspect_rlbench_data(data_path)

    if success:
        print("\n✅ 数据检查完成！请根据上面的推荐配置更新 data/oxe/configs.py 中的 rlbench/1.0.0 配置。")
    else:
        print("\n❌ 数据检查失败，请检查数据路径和格式。")

if __name__ == "__main__":
    main()